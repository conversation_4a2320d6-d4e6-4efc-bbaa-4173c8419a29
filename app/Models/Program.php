<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Program extends Model
{
    use HasFactory;

    protected $fillable = [
        'academy_id',
        'name',
        'days',
        'classes',
        'price',
        'currency',
        'start_time',
        'end_time',
        'max_students',
        'status',
        'description',
    ];

    protected $casts = [
        'days' => 'array',
        'price' => 'decimal:2',
        'classes' => 'integer',
        'max_students' => 'integer',
        'status' => 'boolean',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_days',
        'formatted_price',
        'status_text',
        'academy_name',
        'branch_name',
        'duration_hours',
        'student_count',
        'enrollment_percentage',
    ];

    /**
     * Get the academy that owns the program.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the students enrolled in this program.
     */
    public function students(): HasMany
    {
        return $this->hasMany(Student::class);
    }

    /**
     * Get the payments for this program.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Scope a query to only include active programs.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include inactive programs.
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', false);
    }

    /**
     * Scope a query to search programs by name or academy name.
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where('name', 'like', "%{$search}%")
            ->orWhereHas('academy', function ($academyQuery) use ($search) {
                $academyQuery->where('name', 'like', "%{$search}%");
            });
    }

    /**
     * Scope a query to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->whereHas('academy', function ($academyQuery) use ($branchId) {
            $academyQuery->where('branch_id', $branchId);
        });
    }

    /**
     * Scope a query to filter by price range.
     */
    public function scopePriceRange(Builder $query, float $minPrice = null, float $maxPrice = null): Builder
    {
        if ($minPrice !== null) {
            $query->where('price', '>=', $minPrice);
        }
        if ($maxPrice !== null) {
            $query->where('price', '<=', $maxPrice);
        }
        return $query;
    }

    /**
     * Scope a query to filter by days.
     */
    public function scopeByDays(Builder $query, array $days): Builder
    {
        foreach ($days as $day) {
            $query->whereJsonContains('days', $day);
        }
        return $query;
    }

    /**
     * Get the formatted days string.
     */
    public function getFormattedDaysAttribute(): string
    {
        if (is_array($this->days)) {
            $dayNames = [
                'SUN' => 'Sunday',
                'MON' => 'Monday',
                'TUE' => 'Tuesday',
                'WED' => 'Wednesday',
                'THU' => 'Thursday',
                'FRI' => 'Friday',
                'SAT' => 'Saturday',
            ];

            $formattedDays = array_map(function ($day) use ($dayNames) {
                return $dayNames[$day] ?? $day;
            }, $this->days);

            return implode(', ', $formattedDays);
        }
        return '';
    }

    /**
     * Get the formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price, 2) . ' ' . ($this->currency ?? 'AED');
    }

    /**
     * Get the status text.
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status ? 'Active' : 'Inactive';
    }

    /**
     * Get the academy name.
     */
    public function getAcademyNameAttribute(): string
    {
        return $this->academy?->name ?? '';
    }

    /**
     * Get the branch name.
     */
    public function getBranchNameAttribute(): string
    {
        return $this->academy?->branch?->name ?? '';
    }

    /**
     * Get the program duration in hours.
     */
    public function getDurationHoursAttribute(): float
    {
        if ($this->start_time && $this->end_time) {
            $start = \Carbon\Carbon::parse($this->start_time);
            $end = \Carbon\Carbon::parse($this->end_time);
            return $start->diffInHours($end);
        }
        return 0;
    }

    /**
     * Get the number of enrolled students.
     */
    public function getStudentCountAttribute(): int
    {
        return $this->students()->count();
    }

    /**
     * Get the enrollment percentage.
     */
    public function getEnrollmentPercentageAttribute(): float
    {
        if ($this->max_students && $this->max_students > 0) {
            return round(($this->student_count / $this->max_students) * 100, 2);
        }
        return 0;
    }

    /**
     * Get program statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_students' => $this->student_count,
            'max_capacity' => $this->max_students ?? 0,
            'enrollment_percentage' => $this->enrollment_percentage,
            'total_revenue' => $this->payments()->sum('amount') ?? 0,
            'average_payment' => $this->payments()->avg('amount') ?? 0,
            'pending_payments' => $this->payments()->where('status', 'pending')->count(),
        ];
    }

    /**
     * Check if program is full.
     */
    public function isFull(): bool
    {
        if (!$this->max_students) {
            return false;
        }
        return $this->student_count >= $this->max_students;
    }

    /**
     * Check if program has available spots.
     */
    public function hasAvailableSpots(): bool
    {
        return !$this->isFull();
    }

    /**
     * Get available spots count.
     */
    public function getAvailableSpots(): int
    {
        if (!$this->max_students) {
            return 999; // Unlimited
        }
        return max(0, $this->max_students - $this->student_count);
    }

    /**
     * Check if program is running on a specific day.
     */
    public function runsOnDay(string $day): bool
    {
        return in_array(strtoupper($day), $this->days ?? []);
    }

    /**
     * Get the next class date.
     */
    public function getNextClassDate(): ?\Carbon\Carbon
    {
        if (empty($this->days)) {
            return null;
        }

        $dayMapping = [
            'SUN' => 0,
            'MON' => 1,
            'TUE' => 2,
            'WED' => 3,
            'THU' => 4,
            'FRI' => 5,
            'SAT' => 6
        ];

        $today = now();
        $nextDates = [];

        foreach ($this->days as $day) {
            if (isset($dayMapping[$day])) {
                $nextDate = $today->copy()->next($dayMapping[$day]);
                $nextDates[] = $nextDate;
            }
        }

        return empty($nextDates) ? null : collect($nextDates)->min();
    }

    /**
     * Format program schedule for display.
     */
    public function getScheduleDisplay(): string
    {
        $schedule = $this->formatted_days;

        if ($this->start_time && $this->end_time) {
            $startTime = \Carbon\Carbon::parse($this->start_time)->format('g:i A');
            $endTime = \Carbon\Carbon::parse($this->end_time)->format('g:i A');
            $schedule .= " ({$startTime} - {$endTime})";
        }

        return $schedule;
    }
}
