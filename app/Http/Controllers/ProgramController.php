<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProgramRequest;
use App\Http\Requests\UpdateProgramRequest;
use App\Models\Program;
use App\Models\Academy;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ProgramsExport;
use Barryvdh\DomPDF\Facade\Pdf;

class ProgramController extends Controller
{
    /**
     * Display a listing of the programs.
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', Program::class);

        $query = Program::with(['academy.branch'])
            ->when($request->search, function ($q, $search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhereHas('academy', function ($academyQuery) use ($search) {
                        $academyQuery->where('name', 'like', "%{$search}%");
                    });
            })
            ->when($request->academy_id, function ($q, $academyId) {
                $q->where('academy_id', $academyId);
            })
            ->when($request->branch_id, function ($q, $branchId) {
                $q->whereHas('academy', function ($academyQuery) use ($branchId) {
                    $academyQuery->where('branch_id', $branchId);
                });
            })
            ->when($request->status !== null, function ($q) use ($request) {
                $q->where('status', $request->status);
            })
            ->when($request->min_price, function ($q, $minPrice) {
                $q->where('price', '>=', $minPrice);
            })
            ->when($request->max_price, function ($q, $maxPrice) {
                $q->where('price', '<=', $maxPrice);
            })
            ->when($request->days, function ($q, $days) {
                $daysArray = is_array($days) ? $days : [$days];
                foreach ($daysArray as $day) {
                    $q->whereJsonContains('days', $day);
                }
            })
            ->orderBy($request->get('sort', 'created_at'), $request->get('direction', 'desc'));

        $programs = $query->paginate(15)->withQueryString();

        // Get filter options
        $academies = Academy::with('branch')->orderBy('name')->get();
        $branches = Branch::orderBy('name')->get();

        // Get statistics
        $statistics = $this->getStatisticsData();

        return view('programs.index', compact('programs', 'academies', 'branches', 'statistics'));
    }

    /**
     * Show the form for creating a new program.
     */
    public function create(): View
    {
        $this->authorize('create', Program::class);

        $academies = Academy::with('branch')->where('status', true)->orderBy('name')->get();
        $branches = Branch::where('status', true)->orderBy('name')->get();

        return view('programs.create', compact('academies', 'branches'));
    }

    /**
     * Store a newly created program in storage.
     */
    public function store(StoreProgramRequest $request): RedirectResponse
    {
        $this->authorize('create', Program::class);

        try {
            DB::beginTransaction();

            $program = Program::create($request->validated());

            DB::commit();

            return redirect()
                ->route('programs.index')
                ->with('success', 'Program created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Program creation failed: ' . $e->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create program. Please try again.');
        }
    }

    /**
     * Display the specified program.
     */
    public function show(Program $program): View
    {
        $this->authorize('view', $program);

        $program->load(['academy.branch']);

        return view('programs.show', compact('program'));
    }

    /**
     * Show the form for editing the specified program.
     */
    public function edit(Program $program): View
    {
        $this->authorize('update', $program);

        $program->load(['academy.branch']);
        $academies = Academy::with('branch')->where('status', true)->orderBy('name')->get();
        $branches = Branch::where('status', true)->orderBy('name')->get();

        return view('programs.edit', compact('program', 'academies', 'branches'));
    }

    /**
     * Update the specified program in storage.
     */
    public function update(UpdateProgramRequest $request, Program $program): RedirectResponse
    {
        $this->authorize('update', $program);

        try {
            DB::beginTransaction();

            $program->update($request->validated());

            DB::commit();

            return redirect()
                ->route('programs.index')
                ->with('success', 'Program updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Program update failed: ' . $e->getMessage());

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update program. Please try again.');
        }
    }

    /**
     * Remove the specified program from storage.
     */
    public function destroy(Program $program): RedirectResponse
    {
        $this->authorize('delete', $program);

        try {
            DB::beginTransaction();

            $program->delete();

            DB::commit();

            return redirect()
                ->route('programs.index')
                ->with('success', 'Program deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Program deletion failed: ' . $e->getMessage());

            return redirect()
                ->back()
                ->with('error', 'Failed to delete program. Please try again.');
        }
    }

    /**
     * Toggle program status.
     */
    public function toggleStatus(Program $program): JsonResponse
    {
        $this->authorize('update', $program);

        try {
            $program->update(['status' => !$program->status]);

            return response()->json([
                'success' => true,
                'message' => 'Program status updated successfully.',
                'status' => $program->status
            ]);
        } catch (\Exception $e) {
            Log::error('Program status toggle failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to update program status.'
            ], 500);
        }
    }

    /**
     * Handle bulk actions on programs.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'program_ids' => 'required|array|min:1',
            'program_ids.*' => 'exists:programs,id'
        ]);

        try {
            DB::beginTransaction();

            $programs = Program::whereIn('id', $request->program_ids)->get();

            // Check authorization for each program
            foreach ($programs as $program) {
                if ($request->action === 'delete') {
                    $this->authorize('delete', $program);
                } else {
                    $this->authorize('update', $program);
                }
            }

            $count = 0;
            switch ($request->action) {
                case 'activate':
                    $count = Program::whereIn('id', $request->program_ids)->update(['status' => true]);
                    break;
                case 'deactivate':
                    $count = Program::whereIn('id', $request->program_ids)->update(['status' => false]);
                    break;
                case 'delete':
                    $count = Program::whereIn('id', $request->program_ids)->delete();
                    break;
            }

            DB::commit();

            $actionText = ucfirst($request->action);
            return response()->json([
                'success' => true,
                'message' => "{$actionText}d {$count} program(s) successfully."
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Program bulk action failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk action.'
            ], 500);
        }
    }

    /**
     * Export programs to Excel.
     */
    public function exportExcel(Request $request)
    {
        $this->authorize('viewAny', Program::class);

        try {
            $query = $this->buildExportQuery($request);
            $programs = $query->get();

            return Excel::download(new ProgramsExport($programs), 'programs_' . now()->format('Y-m-d_H-i-s') . '.xlsx');
        } catch (\Exception $e) {
            Log::error('Program Excel export failed: ' . $e->getMessage());

            return redirect()
                ->back()
                ->with('error', 'Failed to export programs to Excel.');
        }
    }

    /**
     * Export programs to PDF.
     */
    public function exportPdf(Request $request)
    {
        $this->authorize('viewAny', Program::class);

        try {
            $query = $this->buildExportQuery($request);
            $programs = $query->get();

            $pdf = Pdf::loadView('programs.export-pdf', compact('programs'));
            $pdf->setPaper('A4', 'landscape');

            return $pdf->download('programs_' . now()->format('Y-m-d_H-i-s') . '.pdf');
        } catch (\Exception $e) {
            Log::error('Program PDF export failed: ' . $e->getMessage());

            return redirect()
                ->back()
                ->with('error', 'Failed to export programs to PDF.');
        }
    }

    /**
     * API endpoint for programs listing.
     */
    public function apiIndex(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Program::class);

        try {
            $query = Program::with(['academy.branch'])
                ->when($request->search, function ($q, $search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhereHas('academy', function ($academyQuery) use ($search) {
                            $academyQuery->where('name', 'like', "%{$search}%");
                        });
                })
                ->when($request->academy_id, function ($q, $academyId) {
                    $q->where('academy_id', $academyId);
                })
                ->when($request->status !== null, function ($q) use ($request) {
                    $q->where('status', $request->status);
                });

            $programs = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $programs
            ]);
        } catch (\Exception $e) {
            Log::error('Program API index failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch programs.'
            ], 500);
        }
    }

    /**
     * Get program statistics.
     */
    public function getStatistics(): JsonResponse
    {
        $this->authorize('viewAny', Program::class);

        try {
            $statistics = $this->getStatisticsData();

            return response()->json([
                'success' => true,
                'data' => $statistics
            ]);
        } catch (\Exception $e) {
            Log::error('Program statistics failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics.'
            ], 500);
        }
    }

    /**
     * Build query for export functionality.
     */
    private function buildExportQuery(Request $request)
    {
        return Program::with(['academy.branch'])
            ->when($request->search, function ($q, $search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhereHas('academy', function ($academyQuery) use ($search) {
                        $academyQuery->where('name', 'like', "%{$search}%");
                    });
            })
            ->when($request->academy_id, function ($q, $academyId) {
                $q->where('academy_id', $academyId);
            })
            ->when($request->branch_id, function ($q, $branchId) {
                $q->whereHas('academy', function ($academyQuery) use ($branchId) {
                    $academyQuery->where('branch_id', $branchId);
                });
            })
            ->when($request->status !== null, function ($q) use ($request) {
                $q->where('status', $request->status);
            })
            ->when($request->min_price, function ($q, $minPrice) {
                $q->where('price', '>=', $minPrice);
            })
            ->when($request->max_price, function ($q, $maxPrice) {
                $q->where('price', '<=', $maxPrice);
            })
            ->when($request->days, function ($q, $days) {
                $daysArray = is_array($days) ? $days : [$days];
                foreach ($daysArray as $day) {
                    $q->whereJsonContains('days', $day);
                }
            })
            ->orderBy('created_at', 'desc');
    }

    /**
     * Get statistics data for programs.
     */
    private function getStatisticsData(): array
    {
        $totalPrograms = Program::count();
        $activePrograms = Program::where('status', true)->count();
        $inactivePrograms = Program::where('status', false)->count();
        $averagePrice = Program::where('status', true)->avg('price') ?? 0;
        $totalRevenue = Program::where('status', true)->sum('price') ?? 0;
        $programsByAcademy = Program::with('academy')
            ->select('academy_id', DB::raw('count(*) as total'))
            ->groupBy('academy_id')
            ->orderByDesc('total')
            ->limit(5)
            ->get();

        return [
            'total_programs' => $totalPrograms,
            'active_programs' => $activePrograms,
            'inactive_programs' => $inactivePrograms,
            'average_price' => round($averagePrice, 2),
            'total_revenue' => $totalRevenue,
            'programs_by_academy' => $programsByAcademy,
            'growth_rate' => $this->calculateGrowthRate(),
        ];
    }

    /**
     * Calculate program growth rate.
     */
    private function calculateGrowthRate(): float
    {
        $currentMonth = Program::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        $lastMonth = Program::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();

        if ($lastMonth == 0) {
            return $currentMonth > 0 ? 100 : 0;
        }

        return round((($currentMonth - $lastMonth) / $lastMonth) * 100, 2);
    }
}
