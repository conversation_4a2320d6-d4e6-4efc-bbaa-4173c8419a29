/* Font Imports - Must come first */
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Branch Management Styles */
@import 'branch-management.css';

/* UAE English Sports Academy - Bank-Style Dashboard CSS */

/* CSS Variables for Bank-Style Design */
:root {
    /* Brand Colors */
    --leaders-red: #E53E3E;
    --leaders-deep-red: #B91C1C;
    --deep-red: #C53030;
    --gold-yellow: #D69E2E;

    /* Neutral Colors */
    --pure-white: #FFFFFF;
    --off-white: #FAFAFA;
    --light-gray: #F7FAFC;
    --medium-gray: #E2E8F0;
    --dark-gray: #4A5568;
    --charcoal-black: #1A202C;

    /* Status Colors */
    --success-green: #38A169;
    --warning-orange: #DD6B20;
    --error-red: #E53E3E;
    --info-blue: #3182CE;

    /* Typography */
    --font-family-primary: 'Century Gothic', 'IBM Plex Sans', system-ui, sans-serif;
    --font-family-arabic: 'IBM Plex Sans Arabic', 'IBM Plex Sans', system-ui, sans-serif;

    /* Shadows */
    --shadow-card: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-bank: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-sidebar: 2px 0 10px rgba(0, 0, 0, 0.1);

    /* Layout */
    --header-height: 64px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 64px;
    --footer-height: 60px;

    /* Border Radius */
    --radius-card: 8px;
    --radius-button: 6px;
    --radius-large: 12px;

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background-color: var(--off-white);
    color: var(--charcoal-black);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
}

/* RTL Support */
[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .sidebar {
    right: 0;
    left: auto;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

[dir="rtl"] .main-content {
    margin-right: var(--sidebar-width);
    margin-left: 0;
}

[dir="rtl"] .sidebar-collapsed .main-content {
    margin-right: var(--sidebar-collapsed-width);
    margin-left: 0;
}

/* Arabic Text */
[lang="ar"], .arabic-text {
    font-family: var(--font-family-arabic);
    direction: rtl;
}

/* Layout Structure */
.dashboard-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    height: var(--header-height);
    background: var(--pure-white);
    border-bottom: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-card);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 var(--space-lg);
}

.sidebar {
    width: var(--sidebar-width);
    background: var(--pure-white);
    border-right: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sidebar);
    position: fixed;
    top: var(--header-height);
    left: 0;
    bottom: 0;
    z-index: 999;
    overflow-y: auto;
    transition: all var(--transition-normal);
}

.sidebar-collapsed {
    width: var(--sidebar-collapsed-width);
}

.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    margin-bottom: var(--footer-height);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
}

.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

.footer {
    height: var(--footer-height);
    background: var(--pure-white);
    border-top: 1px solid var(--medium-gray);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 998;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--space-lg);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }

    [dir="rtl"] .sidebar.mobile-open {
        transform: translateX(0);
    }

    [dir="rtl"] .main-content {
        margin-right: 0;
    }
}

/* Bank-Style Components */

/* Header Components */
.header-logo {
    height: 50px;
    width: auto;
    object-fit: contain;
    transition: all var(--transition-fast);
}

.header-logo:hover {
    transform: scale(1.05);
}

.header-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--charcoal-black);
    font-weight: 600;
    font-size: 1.125rem;
    transition: color var(--transition-fast);
}

.header-brand:hover {
    color: var(--leaders-red);
    text-decoration: none;
}

/* Remove header brand text styles since we're only using logo */

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-left: auto;
}

[dir="rtl"] .header-actions {
    margin-left: 0;
    margin-right: auto;
}

/* Language Toggle */
.language-toggle {
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-button);
    padding: var(--space-sm) var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
}

.language-toggle:hover {
    background: var(--medium-gray);
    border-color: var(--leaders-red);
}

.language-toggle.active {
    background: var(--leaders-red);
    color: var(--pure-white);
    border-color: var(--leaders-red);
}

/* User Profile Dropdown */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-button);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
}

.user-profile:hover {
    background: var(--medium-gray);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--leaders-red);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--pure-white);
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

[dir="rtl"] .user-info {
    align-items: flex-end;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--charcoal-black);
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--dark-gray);
    line-height: 1.2;
}

/* Sidebar Components */
.sidebar-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--medium-gray);
    background: var(--off-white);
}

.sidebar-nav {
    padding: var(--space-md) 0;
}

.nav-item {
    margin: 0 var(--space-md) var(--space-sm);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--space-md);
    border-radius: var(--radius-button);
    text-decoration: none;
    color: var(--dark-gray);
    font-weight: 500;
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    background: var(--light-gray);
    color: var(--leaders-red);
    text-decoration: none;
}

.nav-link.active {
    background: var(--leaders-red);
    color: var(--pure-white);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gold-yellow);
    border-radius: 0 var(--radius-button) var(--radius-button) 0;
}

[dir="rtl"] .nav-link.active::before {
    left: auto;
    right: 0;
    border-radius: var(--radius-button) 0 0 var(--radius-button);
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--space-md);
    flex-shrink: 0;
}

[dir="rtl"] .nav-icon {
    margin-right: 0;
    margin-left: var(--space-md);
}

.nav-text {
    font-size: 0.875rem;
    transition: opacity var(--transition-fast);
}

.sidebar-collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-collapsed .nav-icon {
    margin-right: 0;
    margin-left: 0;
}

/* Sidebar Toggle */
.sidebar-toggle {
    position: absolute;
    top: 50%;
    right: -12px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1001;
}

[dir="rtl"] .sidebar-toggle {
    right: auto;
    left: -12px;
}

.sidebar-toggle:hover {
    background: var(--leaders-red);
    color: var(--pure-white);
    border-color: var(--leaders-red);
}

.sidebar-toggle svg {
    width: 12px;
    height: 12px;
    transition: transform var(--transition-fast);
}

.sidebar-collapsed .sidebar-toggle svg {
    transform: rotate(180deg);
}

/* Bank-Style Cards */
.bank-card {
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-card);
    padding: var(--space-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.bank-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--leaders-red), var(--gold-yellow));
}

.bank-card:hover {
    box-shadow: var(--shadow-bank);
    transform: translateY(-2px);
}

.bank-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 1px solid var(--light-gray);
}

.bank-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--charcoal-black);
    margin: 0;
}

.bank-card-subtitle {
    font-size: 0.875rem;
    color: var(--dark-gray);
    margin: var(--space-xs) 0 0;
}

.bank-card-body {
    flex: 1;
}

.bank-card-footer {
    margin-top: var(--space-lg);
    padding-top: var(--space-md);
    border-top: 1px solid var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, var(--pure-white) 0%, var(--off-white) 100%);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-large);
    padding: var(--space-xl);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--leaders-red);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-bank);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-lg);
    background: linear-gradient(135deg, var(--leaders-red), var(--deep-red));
    color: var(--pure-white);
    font-size: 1.5rem;
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--charcoal-black);
    line-height: 1;
    margin-bottom: var(--space-sm);
}

.stats-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--dark-gray);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-sm);
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.stats-change.positive {
    color: var(--success-green);
}

.stats-change.negative {
    color: var(--error-red);
}

/* Bank-Style Buttons */
.btn-bank {
    background: linear-gradient(135deg, var(--leaders-red) 0%, var(--leaders-deep-red) 100%);
    color: var(--pure-white);
    border: 1px solid var(--leaders-deep-red);
    border-radius: var(--radius-button);
    padding: var(--space-md) var(--space-lg);
    font-family: var(--font-family-primary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-card);
}

.btn-bank::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-bank:hover::before {
    left: 100%;
}

.btn-bank:hover {
    background: linear-gradient(135deg, var(--leaders-deep-red) 0%, #991B1B 100%);
    border-color: #991B1B;
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
    color: var(--pure-white);
    text-decoration: none;
}

.btn-bank:active {
    transform: translateY(0);
}

.btn-bank-secondary {
    background: transparent;
    color: var(--leaders-red);
    border: 1px solid var(--leaders-red);
}

.btn-bank-secondary:hover {
    background: var(--leaders-red);
    color: var(--pure-white);
}

.btn-bank-outline {
    background: transparent;
    color: var(--dark-gray);
    border: 1px solid var(--medium-gray);
}

.btn-bank-outline:hover {
    background: var(--light-gray);
    border-color: var(--leaders-red);
    color: var(--leaders-red);
}

/* Bank-Style Forms */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--charcoal-black);
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-control-bank {
    width: 100%;
    padding: var(--space-md);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-button);
    font-family: var(--font-family-primary);
    font-size: 0.875rem;
    background: var(--pure-white);
    transition: all var(--transition-fast);
    outline: none;
}

.form-control-bank:focus {
    border-color: var(--leaders-red);
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-control-bank:invalid {
    border-color: var(--error-red);
}

.form-control-bank:valid {
    border-color: var(--success-green);
}

/* Bank-Style Tables */
.table-bank {
    width: 100%;
    background: var(--pure-white);
    border-radius: var(--radius-card);
    overflow: hidden;
    box-shadow: var(--shadow-card);
    border-collapse: separate;
    border-spacing: 0;
}

.table-bank thead {
    background: linear-gradient(135deg, var(--charcoal-black), var(--dark-gray));
}

.table-bank thead th {
    padding: var(--space-lg);
    color: var(--pure-white);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    border: none;
    text-align: left;
}

[dir="rtl"] .table-bank thead th {
    text-align: right;
}

.table-bank tbody tr {
    transition: all var(--transition-fast);
    border-bottom: 1px solid var(--light-gray);
}

.table-bank tbody tr:hover {
    background: var(--off-white);
    transform: scale(1.01);
}

.table-bank tbody tr:last-child {
    border-bottom: none;
}

.table-bank tbody td {
    padding: var(--space-lg);
    color: var(--charcoal-black);
    font-size: 0.875rem;
    border: none;
    vertical-align: middle;
}

/* Status Badges */
.badge-bank {
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-button);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
}

.badge-success {
    background: var(--success-green);
    color: var(--pure-white);
}

.badge-warning {
    background: var(--warning-orange);
    color: var(--pure-white);
}

.badge-error {
    background: var(--error-red);
    color: var(--pure-white);
}

.badge-info {
    background: var(--info-blue);
    color: var(--pure-white);
}

.badge-neutral {
    background: var(--medium-gray);
    color: var(--charcoal-black);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
    animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--light-gray) 25%, var(--medium-gray) 50%, var(--light-gray) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    :root {
        --space-lg: 1rem;
        --space-xl: 1.5rem;
    }

    .header {
        padding: 0 var(--space-md);
    }

    .header-brand-text {
        display: none;
    }

    .main-content {
        padding: var(--space-md);
    }

    .stats-card {
        padding: var(--space-lg);
    }

    .stats-value {
        font-size: 2rem;
    }

    .bank-card {
        padding: var(--space-md);
    }

    .table-bank {
        font-size: 0.75rem;
    }

    .table-bank thead th,
    .table-bank tbody td {
        padding: var(--space-md);
    }

    /* Mobile Card Layout for Tables */
    .table-mobile {
        display: none;
    }

    .card-mobile {
        display: block;
        background: var(--pure-white);
        border: 1px solid var(--medium-gray);
        border-radius: var(--radius-card);
        padding: var(--space-lg);
        margin-bottom: var(--space-md);
        box-shadow: var(--shadow-card);
    }

    .card-mobile-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-md);
        padding-bottom: var(--space-md);
        border-bottom: 1px solid var(--light-gray);
    }

    .card-mobile-title {
        font-weight: 600;
        color: var(--charcoal-black);
    }

    .card-mobile-body {
        display: grid;
        gap: var(--space-sm);
    }

    .card-mobile-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-sm) 0;
    }

    .card-mobile-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--dark-gray);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .card-mobile-value {
        font-size: 0.875rem;
        color: var(--charcoal-black);
        text-align: right;
    }

    [dir="rtl"] .card-mobile-value {
        text-align: left;
    }

    .card-mobile-actions {
        margin-top: var(--space-md);
        padding-top: var(--space-md);
        border-top: 1px solid var(--light-gray);
        display: flex;
        gap: var(--space-sm);
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .header-actions {
        gap: var(--space-sm);
    }

    .user-info {
        display: none;
    }

    .stats-card {
        text-align: center;
    }

    .stats-icon {
        margin: 0 auto var(--space-md);
    }

    .btn-bank {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.75rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header,
    .footer,
    .sidebar-toggle,
    .mobile-overlay {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .bank-card,
    .stats-card {
        box-shadow: none !important;
        border: 1px solid var(--medium-gray) !important;
        break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --pure-white: #FFFFFF;
        --charcoal-black: #000000;
        --medium-gray: #666666;
        --leaders-red: #CC0000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Branch Management Specific Styles */
.stats-card-sm {
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-card);
    padding: var(--space-lg);
    box-shadow: var(--shadow-card);
    transition: all var(--transition-fast);
    text-align: center;
}

.stats-card-sm:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.stats-icon-sm {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-card);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-sm);
    color: var(--pure-white);
}

.stats-value-sm {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--charcoal-black);
    margin-bottom: var(--space-xs);
}

.stats-label-sm {
    font-size: 0.75rem;
    color: var(--dark-gray);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

/* Form Enhancements */
.form-label-bank {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--charcoal-black);
    margin-bottom: var(--space-sm);
}

.form-label-bank.required::after {
    content: ' *';
    color: var(--error-red);
}

.form-input-bank,
.form-select-bank,
.form-textarea-bank {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-button);
    font-size: 0.875rem;
    color: var(--charcoal-black);
    background-color: var(--pure-white);
    transition: all var(--transition-fast);
}

.form-input-bank:focus,
.form-select-bank:focus,
.form-textarea-bank:focus {
    outline: none;
    border-color: var(--leaders-red);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-input-bank::placeholder,
.form-textarea-bank::placeholder {
    color: var(--medium-gray);
}

.form-checkbox-bank,
.form-radio-bank {
    width: 1rem;
    height: 1rem;
    color: var(--leaders-red);
    border: 1px solid var(--medium-gray);
    border-radius: 0.25rem;
    transition: all var(--transition-fast);
}

.form-checkbox-bank:focus,
.form-radio-bank:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-error-bank {
    color: var(--error-red);
    font-size: 0.75rem;
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
}

.form-error-bank::before {
    content: '⚠';
    margin-right: var(--space-xs);
}

.form-help-bank {
    color: var(--medium-gray);
    font-size: 0.75rem;
    margin-top: var(--space-xs);
}

/* Enhanced Button Styles */
.btn-bank-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
}

.btn-bank-secondary {
    background-color: var(--off-white);
    color: var(--charcoal-black);
    border: 1px solid var(--medium-gray);
}

.btn-bank-secondary:hover {
    background-color: var(--light-gray);
    border-color: var(--dark-gray);
}

/* Table Enhancements */
.table-bank {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.table-bank thead {
    background: linear-gradient(135deg, var(--charcoal-black) 0%, var(--dark-gray) 100%);
}

.table-bank thead th {
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--pure-white);
    border-bottom: 2px solid var(--leaders-red);
    position: relative;
}

.table-bank thead th:first-child {
    border-top-left-radius: var(--radius-card);
}

.table-bank thead th:last-child {
    border-top-right-radius: var(--radius-card);
}

.table-bank tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--light-gray);
    vertical-align: middle;
}

.table-bank tbody tr:hover {
    background-color: var(--off-white);
}

.table-bank tbody tr:last-child td {
    border-bottom: none;
}

/* Badge Enhancements */
.badge-bank {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: var(--success-green);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.badge-neutral {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--medium-gray);
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-orange);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error-red);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-blue);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Animation Enhancements */
.scale-in {
    animation: scaleIn 0.3s ease-out forwards;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Mobile Responsive Enhancements for Branch Management */
@media (max-width: 768px) {
    .stats-card {
        padding: var(--space-md);
    }

    .stats-value {
        font-size: 1.5rem;
    }

    .bank-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-sm);
    }

    .form-input-bank,
    .form-select-bank,
    .form-textarea-bank {
        font-size: 1rem; /* Prevent zoom on iOS */
    }

    .btn-bank {
        width: 100%;
        justify-content: center;
    }

    .table-bank {
        font-size: 0.75rem;
    }

    .table-bank thead th,
    .table-bank tbody td {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .stats-card-sm {
        padding: var(--space-md);
    }

    .stats-icon-sm {
        width: 2.5rem;
        height: 2.5rem;
    }

    .stats-value-sm {
        font-size: 1.25rem;
    }

    .form-label-bank {
        font-size: 0.75rem;
    }

    .btn-bank-sm {
        padding: 0.375rem 0.5rem;
        font-size: 0.625rem;
    }
}

/* Action Buttons for Tables */
.btn-action {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: var(--radius-button);
    border: 1px solid var(--medium-gray);
    background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%); /* Light red background */
    color: var(--dark-gray);
    transition: all var(--transition-fast);
    cursor: pointer;
    text-decoration: none;
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-hover);
    border-color: var(--leaders-red);
}

.btn-action:active {
    transform: translateY(0);
    box-shadow: var(--shadow-card);
}

/* Action Button Variants */
.btn-action-view {
    color: var(--info-blue);
    border-color: rgba(49, 130, 206, 0.2);
}

.btn-action-view:hover {
    background: var(--info-blue);
    color: var(--pure-white);
    border-color: var(--info-blue);
}

.btn-action-edit {
    color: var(--warning-orange);
    border-color: rgba(221, 107, 32, 0.2);
}

.btn-action-edit:hover {
    background: var(--warning-orange);
    color: var(--pure-white);
    border-color: var(--warning-orange);
}

.btn-action-delete {
    color: var(--error-red);
    border-color: rgba(229, 62, 62, 0.2);
}

.btn-action-delete:hover {
    background: var(--error-red);
    color: var(--pure-white);
    border-color: var(--error-red);
}

/* Action Button Group */
.btn-action-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

/* Table Action Column */
.table-bank .actions-column {
    width: 120px;
    text-align: center;
    padding: var(--space-md);
}

.table-bank .actions-column .btn-action-group {
    justify-content: center;
}

/* Mobile Action Buttons */
@media (max-width: 768px) {
    .btn-action {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.75rem;
    }

    .btn-action-group {
        gap: var(--space-xs);
        flex-wrap: wrap;
        justify-content: center;
    }

    .table-bank .actions-column {
        width: 100px;
        padding: var(--space-sm);
    }
}

/* Enhanced Table Styling */
.table-bank tbody tr {
    transition: all var(--transition-fast);
}

.table-bank tbody tr:hover {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-gray) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Badge Enhancements */
.badge-bank {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all var(--transition-fast);
}

.badge-success {
    background: linear-gradient(135deg, var(--success-green) 0%, #2F855A 100%);
    color: var(--pure-white);
    border: 1px solid var(--success-green);
}

.badge-neutral {
    background: linear-gradient(135deg, var(--medium-gray) 0%, var(--dark-gray) 100%);
    color: var(--pure-white);
    border: 1px solid var(--medium-gray);
}

/* Card Hover Effects */
.bank-card:hover .btn-action {
    transform: scale(1.05);
}

.bank-card:hover .btn-action-group {
    gap: var(--space-sm);
}
