<!-- Grid View -->
<div class="p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        @foreach ($academies as $academy)
            <div
                class="bg-white border border-medium-gray rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                <!-- Card Header -->
                <div class="p-6 pb-4">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-4">
                            @can('bulkAction', App\Models\Academy::class)
                                <input type="checkbox" name="academy_ids[]" value="{{ $academy->id }}"
                                    @change="toggleAcademySelection({{ $academy->id }})"
                                    :checked="selectedAcademies.includes({{ $academy->id }})" class="form-checkbox-bank">
                            @endcan
                            <div
                                class="w-14 h-14 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-xl flex items-center justify-center shadow-lg">
                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                    </path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-bold text-lg text-charcoal-black mb-1">{{ $academy->name }}</h4>
                                <p class="text-sm text-dark-gray flex items-center">
                                    <svg class="w-4 h-4 mr-1.5 text-medium-gray" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                        </path>
                                    </svg>
                                    {{ $academy->branch->name ?? 'No branch assigned' }}
                                </p>
                                <p class="text-xs text-medium-gray">ID: #{{ $academy->id }}</p>
                            </div>
                        </div>
                        <button @click="toggleStatus({{ $academy->id }})"
                            class="badge-bank {{ $academy->status ? 'badge-success' : 'badge-neutral' }} px-3 py-1.5 text-sm font-medium cursor-pointer hover:opacity-80 transition-opacity">
                            {{ $academy->status ? 'Active' : 'Inactive' }}
                        </button>
                    </div>

                    <!-- Description -->
                    @if ($academy->description)
                        <div class="mb-4">
                            <p class="text-sm text-dark-gray">{{ Str::limit($academy->description, 120) }}</p>
                        </div>
                    @endif

                    <!-- Coach Information -->
                    @if ($academy->coach_name)
                        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-medium-gray" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span class="text-sm font-medium text-dark-gray">Coach:</span>
                            </div>
                            <div class="mt-1">
                                <p class="text-sm font-semibold text-charcoal-black">{{ $academy->coach_name }}</p>
                                @if ($academy->formatted_coach_phone)
                                    <p class="text-xs text-dark-gray">{{ $academy->formatted_coach_phone }}</p>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Statistics Grid -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div
                            class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 text-center border border-blue-200">
                            <div class="text-2xl font-bold text-blue-600 mb-1">
                                {{ $academy->programs_count ?? 0 }}
                            </div>
                            <div class="text-xs font-medium text-blue-700 uppercase tracking-wide">Programs</div>
                        </div>
                        <div
                            class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 text-center border border-green-200">
                            <div class="text-2xl font-bold text-green-600 mb-1">
                                {{ $academy->students_count ?? 0 }}
                            </div>
                            <div class="text-xs font-medium text-green-700 uppercase tracking-wide">Students</div>
                        </div>
                    </div>

                    <!-- Revenue Information -->
                    <div
                        class="bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg p-4 border border-emerald-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs font-medium text-emerald-700 uppercase tracking-wide">Total Revenue
                                </p>
                                <p class="text-lg font-bold text-emerald-900">AED
                                    {{ number_format($academy->total_revenue, 2) }}</p>
                                @if ($academy->pending_payments > 0)
                                    <p class="text-xs text-amber-600">+AED
                                        {{ number_format($academy->pending_payments, 2) }} pending</p>
                                @endif
                            </div>
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                    </path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="btn-action-group">
                            @can('view', $academy)
                                <a href="{{ route('academies.show', $academy) }}" class="btn-action btn-action-view"
                                    title="View Academy">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan
                            @can('update', $academy)
                                <a href="{{ route('academies.edit', $academy) }}" class="btn-action btn-action-edit"
                                    title="Edit Academy">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan
                            @can('delete', $academy)
                                <button @click="deleteAcademy({{ $academy->id }})" class="btn-action btn-action-delete"
                                    title="Delete Academy">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                        <div class="text-xs text-medium-gray font-medium">
                            {{ $academy->created_at->format('M d, Y') }}
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    @if ($academies->isEmpty())
        <div class="text-center py-12">
            <div class="flex flex-col items-center justify-center space-y-4">
                <svg class="w-16 h-16 text-medium-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l9-5-9-5-9 5 9 5z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                    </path>
                </svg>
                <div class="text-lg font-medium text-dark-gray">No academies found</div>
                <div class="text-sm text-medium-gray">Try adjusting your search criteria or create a new academy</div>
                @can('create', App\Models\Academy::class)
                    <a href="{{ route('academies.create') }}" class="btn-bank btn-bank-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Academy
                    </a>
                @endcan
            </div>
        </div>
    @endif
</div>
