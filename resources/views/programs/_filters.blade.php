<!-- Advanced Search & Filters -->
<div class="bank-card mb-6" x-data="programFilters()" x-init="init()">
    <div class="bank-card-header">
        <div class="flex items-center justify-between w-full">
            <div>
                <h3 class="bank-card-title">Search & Filters</h3>
                <p class="bank-card-subtitle">Find programs by name, academy, price, or schedule</p>
            </div>
            <button @click="toggleFilters()" class="btn-bank btn-bank-outline btn-bank-sm">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                    </path>
                </svg>
                <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
            </button>
        </div>
    </div>

    <div class="bank-card-body">
        <!-- Search Bar -->
        <form method="GET" action="{{ route('programs.index') }}" class="space-y-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:space-x-4 space-y-4 lg:space-y-0">
                <!-- Main Search -->
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-medium-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" name="search" value="{{ request('search') }}"
                            placeholder="Search programs by name or academy..."
                            class="form-input-bank pl-10 w-full">
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center space-x-2">
                    <button type="submit" class="btn-bank">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    <a href="{{ route('programs.index') }}" class="btn-bank btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                            </path>
                        </svg>
                        Reset
                    </a>
                </div>
            </div>

            <!-- Advanced Filters -->
            <div x-show="showFilters" x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform -translate-y-2"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100 transform translate-y-0"
                x-transition:leave-end="opacity-0 transform -translate-y-2">

                <div class="border-t border-light-gray pt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Branch Filter -->
                        <div>
                            <label class="form-label-bank">Branch</label>
                            <select name="branch_id" class="form-select-bank" @change="onBranchChange($event)">
                                <option value="">All Branches</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Academy Filter -->
                        <div>
                            <label class="form-label-bank">Academy</label>
                            <select name="academy_id" class="form-select-bank">
                                <option value="">All Academies</option>
                                @foreach($academies as $academy)
                                    <option value="{{ $academy->id }}" 
                                        {{ request('academy_id') == $academy->id ? 'selected' : '' }}
                                        data-branch-id="{{ $academy->branch_id }}">
                                        {{ $academy->name }} ({{ $academy->branch->name }})
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Status Filter -->
                        <div>
                            <label class="form-label-bank">Status</label>
                            <select name="status" class="form-select-bank">
                                <option value="">All Status</option>
                                <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Active</option>
                                <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>

                        <!-- Sort By -->
                        <div>
                            <label class="form-label-bank">Sort By</label>
                            <select name="sort" class="form-select-bank">
                                <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Date Created</option>
                                <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Program Name</option>
                                <option value="price" {{ request('sort') === 'price' ? 'selected' : '' }}>Price</option>
                                <option value="classes" {{ request('sort') === 'classes' ? 'selected' : '' }}>Classes</option>
                            </select>
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
                        <div>
                            <label class="form-label-bank">Min Price (AED)</label>
                            <input type="number" name="min_price" value="{{ request('min_price') }}" 
                                placeholder="0" min="0" step="0.01" class="form-input-bank">
                        </div>

                        <div>
                            <label class="form-label-bank">Max Price (AED)</label>
                            <input type="number" name="max_price" value="{{ request('max_price') }}" 
                                placeholder="10000" min="0" step="0.01" class="form-input-bank">
                        </div>

                        <div>
                            <label class="form-label-bank">Sort Direction</label>
                            <select name="direction" class="form-select-bank">
                                <option value="desc" {{ request('direction') === 'desc' ? 'selected' : '' }}>Descending</option>
                                <option value="asc" {{ request('direction') === 'asc' ? 'selected' : '' }}>Ascending</option>
                            </select>
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="btn-bank w-full">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                                    </path>
                                </svg>
                                Apply Filters
                            </button>
                        </div>
                    </div>

                    <!-- Days Filter -->
                    <div class="mt-4">
                        <label class="form-label-bank">Program Days</label>
                        <div class="flex flex-wrap gap-2 mt-2">
                            @php
                                $days = ['SUN' => 'Sunday', 'MON' => 'Monday', 'TUE' => 'Tuesday', 'WED' => 'Wednesday', 'THU' => 'Thursday', 'FRI' => 'Friday', 'SAT' => 'Saturday'];
                                $selectedDays = request('days', []);
                            @endphp
                            @foreach($days as $value => $label)
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="days[]" value="{{ $value }}" 
                                        {{ in_array($value, $selectedDays) ? 'checked' : '' }}
                                        class="form-checkbox text-leaders-red border-medium-gray rounded focus:ring-leaders-red focus:ring-offset-0">
                                    <span class="ml-2 text-sm text-dark-gray">{{ $label }}</span>
                                </label>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
    function programFilters() {
        return {
            showFilters: {{ request()->hasAny(['branch_id', 'academy_id', 'status', 'min_price', 'max_price', 'days', 'sort']) ? 'true' : 'false' }},

            init() {
                // Initialize any filter-specific functionality
            },

            toggleFilters() {
                this.showFilters = !this.showFilters;
            },

            onBranchChange(event) {
                const branchId = event.target.value;
                const academySelect = document.querySelector('select[name="academy_id"]');
                const academyOptions = academySelect.querySelectorAll('option');

                // Show/hide academy options based on selected branch
                academyOptions.forEach(option => {
                    if (option.value === '') {
                        option.style.display = 'block';
                        return;
                    }

                    const optionBranchId = option.dataset.branchId;
                    if (!branchId || optionBranchId === branchId) {
                        option.style.display = 'block';
                    } else {
                        option.style.display = 'none';
                        if (option.selected) {
                            option.selected = false;
                            academySelect.value = '';
                        }
                    }
                });
            }
        }
    }
</script>
@endpush
