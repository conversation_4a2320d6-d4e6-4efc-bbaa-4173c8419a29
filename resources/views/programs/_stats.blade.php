<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
    <!-- Total Programs Card -->
    <div class="bank-card bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-700 uppercase tracking-wide">Total Programs</p>
                <p class="text-3xl font-bold text-blue-900">{{ number_format($statistics['total_programs']) }}</p>
                <div class="flex items-center mt-2">
                    <svg class="w-4 h-4 text-blue-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                        </path>
                    </svg>
                    <span class="text-xs text-blue-600 font-medium">All Programs</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                    </path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Active Programs Card -->
    <div class="bank-card bg-gradient-to-br from-green-50 to-green-100 border-green-200">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-green-700 uppercase tracking-wide">Active Programs</p>
                <p class="text-3xl font-bold text-green-900">{{ number_format($statistics['active_programs']) }}</p>
                <div class="flex items-center mt-2">
                    <svg class="w-4 h-4 text-green-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-xs text-green-600 font-medium">Currently Running</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Average Price Card -->
    <div class="bank-card bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-yellow-700 uppercase tracking-wide">Average Price</p>
                <p class="text-3xl font-bold text-yellow-900">{{ number_format($statistics['average_price'], 2) }} AED</p>
                <div class="flex items-center mt-2">
                    <svg class="w-4 h-4 text-yellow-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    <span class="text-xs text-yellow-600 font-medium">Per Program</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Growth Rate Card -->
    <div class="bank-card bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-purple-700 uppercase tracking-wide">Growth Rate</p>
                <p class="text-3xl font-bold text-purple-900">
                    {{ $statistics['growth_rate'] > 0 ? '+' : '' }}{{ number_format($statistics['growth_rate'], 1) }}%
                </p>
                <div class="flex items-center mt-2">
                    @if($statistics['growth_rate'] >= 0)
                        <svg class="w-4 h-4 text-purple-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span class="text-xs text-purple-600 font-medium">This Month</span>
                    @else
                        <svg class="w-4 h-4 text-red-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                        </svg>
                        <span class="text-xs text-red-600 font-medium">This Month</span>
                    @endif
                </div>
            </div>
            <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                    </path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics Row -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Total Revenue Card -->
    <div class="bank-card bg-gradient-to-br from-red-50 to-red-100 border-red-200">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <p class="text-sm font-medium text-red-700 uppercase tracking-wide mb-2">Total Revenue</p>
                <p class="text-4xl font-bold text-red-900 mb-2">{{ number_format($statistics['total_revenue'], 2) }} AED</p>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-red-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z">
                            </path>
                        </svg>
                        <span class="text-xs text-red-600 font-medium">From Active Programs</span>
                    </div>
                    <div class="text-right">
                        <p class="text-xs text-red-600">{{ $statistics['active_programs'] }} Programs</p>
                        <p class="text-xs text-red-500">{{ $statistics['inactive_programs'] }} Inactive</p>
                    </div>
                </div>
            </div>
            <div class="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center ml-4">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z">
                    </path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Top Academies Card -->
    <div class="bank-card bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200">
        <div>
            <p class="text-sm font-medium text-indigo-700 uppercase tracking-wide mb-4">Top Academies by Programs</p>
            <div class="space-y-3">
                @forelse($statistics['programs_by_academy']->take(3) as $academyProgram)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs font-bold">{{ $loop->iteration }}</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-indigo-900">{{ $academyProgram->academy->name ?? 'Unknown Academy' }}</p>
                                <p class="text-xs text-indigo-600">{{ $academyProgram->academy->branch->name ?? 'Unknown Branch' }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-indigo-900">{{ $academyProgram->total }}</p>
                            <p class="text-xs text-indigo-600">Programs</p>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-4">
                        <p class="text-sm text-indigo-600">No program data available</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
