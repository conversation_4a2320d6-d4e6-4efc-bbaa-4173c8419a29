{{-- Premium Bank-Style Table Component --}}
{{-- Responsive table with mobile card layout --}}

<div class="overflow-hidden">
    <!-- Desktop/Tablet Table View -->
    <div class="hidden md:block overflow-x-auto">
        <table class="table-bank w-full">
            <thead>
                <tr>
                    <th class="w-12">
                        <div class="flex items-center">
                            <input type="checkbox" class="form-checkbox-bank" @change="selectAllBranches()"
                                :checked="selectedBranches.length === {{ $branches->count() }} && {{ $branches->count() }} > 0">
                        </div>
                    </th>
                    <th class="text-left">
                        <div class="flex items-center space-x-2">
                            <span>Branch Details</span>
                            <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                            </svg>
                        </div>
                    </th>
                    <th class="text-center">Status</th>
                    <th class="text-center">Academies</th>
                    <th class="text-center">Students</th>
                    <th class="text-center">Revenue</th>
                    <th class="text-center">Created</th>
                    <th class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($branches as $branch)
                    <tr class="hover:bg-off-white transition-colors duration-200" x-data="{ showDetails: false }">
                        <td>
                            <input type="checkbox" name="branch_ids[]" value="{{ $branch->id }}"
                                class="form-checkbox-bank" @change="toggleBranchSelection({{ $branch->id }})"
                                :checked="selectedBranches.includes({{ $branch->id }})">
                        </td>
                        <td>
                            <div class="flex items-center space-x-4">
                                <div
                                    class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                        </path>
                                    </svg>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <div class="font-semibold text-charcoal-black truncate">{{ $branch->name }}</div>
                                    <div class="text-sm text-dark-gray truncate">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        {{ $branch->location }}
                                    </div>
                                    @if ($branch->phone || $branch->email)
                                        <div class="text-xs text-medium-gray mt-1">
                                            @if ($branch->phone)
                                                <span class="mr-3">
                                                    <svg class="w-3 h-3 inline mr-1" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                                        </path>
                                                    </svg>
                                                    {{ $branch->formatted_phone }}
                                                </span>
                                            @endif
                                            @if ($branch->email)
                                                <span>
                                                    <svg class="w-3 h-3 inline mr-1" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                                        </path>
                                                    </svg>
                                                    {{ $branch->email }}
                                                </span>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            <button @click="toggleBranchStatus({{ $branch->id }})"
                                class="badge-bank {{ $branch->status ? 'badge-success' : 'badge-neutral' }} cursor-pointer hover:opacity-80 transition-opacity">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="{{ $branch->status ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                                    </path>
                                </svg>
                                {{ $branch->status ? 'Active' : 'Inactive' }}
                            </button>
                        </td>
                        <td class="text-center">
                            <div class="font-semibold text-leaders-red">{{ $branch->academies_count ?? 0 }}</div>
                            <div class="text-xs text-dark-gray">Academies</div>
                        </td>
                        <td class="text-center">
                            <div class="font-semibold text-success-green">{{ $branch->students_count ?? 0 }}</div>
                            <div class="text-xs text-dark-gray">Students</div>
                        </td>
                        <td class="text-center">
                            <div class="font-semibold text-info-blue">
                                {{ number_format($branch->total_revenue ?? 0, 0) }}
                            </div>
                            <div class="text-xs text-dark-gray">AED</div>
                        </td>
                        <td class="text-center">
                            <div class="text-sm text-charcoal-black">{{ $branch->created_at->format('M d, Y') }}</div>
                            <div class="text-xs text-dark-gray">{{ $branch->created_at->format('g:i A') }}</div>
                        </td>
                        <td class="text-center">
                            <div class="flex items-center justify-center space-x-2">
                                @can('view', $branch)
                                    <a href="{{ route('branches.show', $branch) }}"
                                        class="btn-bank btn-bank-sm btn-bank-outline" title="View Details">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                            </path>
                                        </svg>
                                    </a>
                                @endcan
                                @can('update', $branch)
                                    <a href="{{ route('branches.edit', $branch) }}" class="btn-bank btn-bank-sm"
                                        title="Edit Branch">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                            </path>
                                        </svg>
                                    </a>
                                @endcan
                                <button @click="deleteBranch({{ $branch->id }}, '{{ $branch->name }}')"
                                    class="btn-bank btn-bank-sm btn-bank-outline text-error-red border-error-red hover:bg-error-red hover:text-white"
                                    title="Delete Branch">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="8" class="text-center py-12">
                            <div class="flex flex-col items-center">
                                <svg class="w-16 h-16 text-medium-gray mb-4" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                                <h3 class="text-lg font-semibold text-charcoal-black mb-2">No Branches Found</h3>
                                <p class="text-dark-gray mb-4">No branches match your current search criteria.</p>
                                <a href="{{ route('branches.create') }}" class="btn-bank">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Create First Branch
                                </a>
                            </div>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Mobile Card View -->
    <div class="md:hidden space-y-4 p-4">
        @forelse($branches as $branch)
            <div
                class="bg-white border border-medium-gray rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
                <!-- Card Header -->
                <div class="p-5 pb-4">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <input type="checkbox" name="branch_ids[]" value="{{ $branch->id }}"
                                class="form-checkbox-bank" @change="toggleBranchSelection({{ $branch->id }})"
                                :checked="selectedBranches.includes({{ $branch->id }})">
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-xl flex items-center justify-center shadow-md">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h4 class="font-bold text-base text-charcoal-black mb-1">{{ $branch->name }}</h4>
                                <p class="text-sm text-dark-gray flex items-center">
                                    <svg class="w-3.5 h-3.5 mr-1.5 text-medium-gray" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                        </path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $branch->location }}
                                </p>
                            </div>
                        </div>
                        <button @click="toggleBranchStatus({{ $branch->id }})"
                            class="badge-bank {{ $branch->status ? 'badge-success' : 'badge-neutral' }} cursor-pointer px-3 py-1.5 text-sm font-medium">
                            {{ $branch->status ? 'Active' : 'Inactive' }}
                        </button>
                    </div>

                    <!-- Statistics Grid -->
                    <div class="grid grid-cols-3 gap-3 mb-4">
                        <div
                            class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-3 text-center border border-red-200">
                            <div class="text-lg font-bold text-leaders-red mb-0.5">{{ $branch->academies_count ?? 0 }}
                            </div>
                            <div class="text-xs font-medium text-red-700 uppercase tracking-wide">Academies</div>
                        </div>
                        <div
                            class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-3 text-center border border-green-200">
                            <div class="text-lg font-bold text-success-green mb-0.5">
                                {{ $branch->students_count ?? 0 }}</div>
                            <div class="text-xs font-medium text-green-700 uppercase tracking-wide">Students</div>
                        </div>
                        <div
                            class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-3 text-center border border-blue-200">
                            <div class="text-lg font-bold text-info-blue mb-0.5">
                                {{ number_format($branch->total_revenue ?? 0, 0) }}</div>
                            <div class="text-xs font-medium text-blue-700 uppercase tracking-wide">AED</div>
                        </div>
                    </div>
                </div>

                @if ($branch->phone || $branch->email)
                    <div class="px-5 pb-4">
                        <div class="text-xs text-medium-gray space-y-2">
                            @if ($branch->phone)
                                <div class="flex items-center">
                                    <svg class="w-3.5 h-3.5 mr-2 text-medium-gray" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                        </path>
                                    </svg>
                                    <span class="font-medium">{{ $branch->formatted_phone }}</span>
                                </div>
                            @endif
                            @if ($branch->email)
                                <div class="flex items-center">
                                    <svg class="w-3.5 h-3.5 mr-2 text-medium-gray" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                    <span class="font-medium">{{ $branch->email }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Card Footer -->
                <div class="px-5 py-4 bg-gray-50 border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            @can('view', $branch)
                                <a href="{{ route('branches.show', $branch) }}"
                                    class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200">
                                    <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                    View
                                </a>
                            @endcan
                            @can('update', $branch)
                                <a href="{{ route('branches.edit', $branch) }}"
                                    class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-leaders-red border border-leaders-red rounded-lg hover:bg-leaders-deep-red transition-colors duration-200">
                                    <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                    Edit
                                </a>
                            @endcan
                        </div>
                        <div class="text-xs text-medium-gray font-medium">
                            {{ $branch->created_at->format('M d, Y') }}
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-medium-gray mb-4 mx-auto" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                    </path>
                </svg>
                <h3 class="text-lg font-semibold text-charcoal-black mb-2">No Branches Found</h3>
                <p class="text-dark-gray mb-4">No branches match your current search criteria.</p>
                <a href="{{ route('branches.create') }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create First Branch
                </a>
            </div>
        @endforelse
    </div>
</div>

<script>
    async function toggleBranchStatus(branchId) {
        try {
            const response = await fetch(`/branches/${branchId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while updating the branch status.');
        }
    }

    async function deleteBranch(branchId, branchName) {
        if (!confirm(`Are you sure you want to delete "${branchName}"? This action cannot be undone.`)) {
            return;
        }

        try {
            const response = await fetch(`/branches/${branchId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while deleting the branch.');
        }
    }
</script>
