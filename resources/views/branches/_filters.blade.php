{{-- Advanced Search & Filters Component --}}
{{-- Premium bank-style filtering with responsive design --}}

<div class="bank-card mb-6" x-data="branchFilters()" x-init="init()">
    <div class="bank-card-header">
        <div>
            <h3 class="bank-card-title">Search & Filters</h3>
            <p class="bank-card-subtitle">Advanced search and filtering options</p>
        </div>
        <div class="flex items-center space-x-2">
            <button @click="toggleFilters()" class="btn-bank btn-bank-outline btn-bank-sm">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                    </path>
                </svg>
                <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
            </button>
            <button @click="clearAllFilters()"
                class="btn-bank btn-bank-outline btn-bank-sm text-error-red border-error-red hover:bg-error-red hover:text-white">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
                Clear All
            </button>
        </div>
    </div>

    <div class="bank-card-body">
        <!-- Premium Quick Search -->
        <div class="mb-8">
            <div class="relative group">
                <!-- Premium Search Input -->
                <input type="text" name="search" value="{{ request('search') }}"
                    placeholder="Search branches by name, location, address, phone, or email..."
                    class="w-full pl-6 pr-20 py-4 text-base font-medium text-charcoal-black placeholder-medium-gray bg-white border-2 border-light-gray rounded-xl shadow-sm focus:border-leaders-red focus:ring-4 focus:ring-leaders-red/10 focus:shadow-lg transition-all duration-300 hover:border-medium-gray hover:shadow-md"
                    x-model="filters.search" @input.debounce.500ms="applyFilters()">

                <!-- Search Icon (Right Side) -->
                <div class="absolute inset-y-0 right-0 pr-16 flex items-center pointer-events-none">
                    <div
                        class="p-2 rounded-lg bg-gradient-to-br from-leaders-red to-leaders-deep-red shadow-lg group-focus-within:shadow-xl transition-all duration-300">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>

                <!-- Clear Button -->
                <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                    <div x-show="filters.search" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 scale-75" x-transition:enter-end="opacity-100 scale-100"
                        x-transition:leave="transition ease-in duration-150"
                        x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-75"
                        @click="filters.search = ''; applyFilters()"
                        class="cursor-pointer p-2 rounded-lg bg-gray-100 hover:bg-gray-200 text-medium-gray hover:text-charcoal-black transition-all duration-200 group">
                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                </div>

                <!-- Search Enhancement Glow -->
                <div
                    class="absolute inset-0 rounded-xl bg-gradient-to-r from-leaders-red/5 to-leaders-deep-red/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none -z-10">
                </div>
            </div>

            <!-- Search Stats -->
            <div class="mt-3 flex items-center justify-between text-sm">
                <div class="flex items-center space-x-4">
                    <span class="text-medium-gray">
                        <svg class="w-4 h-4 inline mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Press Enter to search instantly
                    </span>
                </div>
                <div x-show="filters.search" class="text-leaders-red font-medium">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Searching...
                </div>
            </div>
        </div>

        <!-- Premium Advanced Filters -->
        <div x-show="showFilters" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform -translate-y-2"
            x-transition:enter-end="opacity-100 transform translate-y-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-y-0"
            x-transition:leave-end="opacity-0 transform -translate-y-2">

            <!-- Filter Section Header -->
            <div class="mb-6 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl border border-gray-200">
                <h4 class="text-lg font-bold text-charcoal-black mb-2 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-leaders-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4">
                        </path>
                    </svg>
                    Advanced Filters
                </h4>
                <p class="text-sm text-medium-gray">Refine your search with detailed criteria</p>
            </div>

            <!-- Primary Filters Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Status Filter -->
                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Status
                    </label>
                    <select name="status"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.status" @change="applyFilters()">
                        <option value="">All Statuses</option>
                        <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>✅ Active</option>
                        <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>❌ Inactive</option>
                    </select>
                </div>

                <!-- Location Filter -->
                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Location
                    </label>
                    <select name="location"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.location" @change="applyFilters()">
                        <option value="">🌍 All Locations</option>
                        @foreach ($availableLocations ?? [] as $location)
                            <option value="{{ $location }}"
                                {{ request('location') == $location ? 'selected' : '' }}>
                                📍 {{ $location }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Date Range From -->
                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        Created From
                    </label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.date_from" @change="applyFilters()">
                </div>

                <!-- Date Range To -->
                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        Created To
                    </label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.date_to" @change="applyFilters()">
                </div>
            </div>

            <!-- Secondary Filters Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Student Count Range -->
                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                        Min Students
                    </label>
                    <input type="number" name="min_students" value="{{ request('min_students') }}" placeholder="0"
                        min="0"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.min_students" @input.debounce.500ms="applyFilters()">
                </div>

                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                        Max Students
                    </label>
                    <input type="number" name="max_students" value="{{ request('max_students') }}"
                        placeholder="1000" min="0"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.max_students" @input.debounce.500ms="applyFilters()">
                </div>

                <!-- Academy Count Range -->
                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                        Min Academies
                    </label>
                    <input type="number" name="min_academies" value="{{ request('min_academies') }}"
                        placeholder="0" min="0"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.min_academies" @input.debounce.500ms="applyFilters()">
                </div>

                <div class="filter-group">
                    <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                        <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                        Max Academies
                    </label>
                    <input type="number" name="max_academies" value="{{ request('max_academies') }}"
                        placeholder="50" min="0"
                        class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-gray-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-gray-300"
                        x-model="filters.max_academies" @input.debounce.500ms="applyFilters()">
                </div>
            </div>

            <!-- Sort & Display Options -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200 mb-8">
                <h5 class="text-md font-bold text-charcoal-black mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                    </svg>
                    Sort & Display Options
                </h5>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Sort By -->
                    <div class="filter-group">
                        <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                            <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"></path>
                            </svg>
                            Sort By
                        </label>
                        <select name="sort_by"
                            class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-blue-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-blue-300"
                            x-model="filters.sort_by" @change="applyFilters()">
                            <option value="name" {{ request('sort_by', 'name') == 'name' ? 'selected' : '' }}>📝
                                Name</option>
                            <option value="location" {{ request('sort_by') == 'location' ? 'selected' : '' }}>📍
                                Location</option>
                            <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>📅
                                Created Date</option>
                            <option value="students_count"
                                {{ request('sort_by') == 'students_count' ? 'selected' : '' }}>👥 Student Count
                            </option>
                            <option value="academies_count"
                                {{ request('sort_by') == 'academies_count' ? 'selected' : '' }}>🏢 Academy Count
                            </option>
                        </select>
                    </div>

                    <!-- Sort Direction -->
                    <div class="filter-group">
                        <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                            <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                            </svg>
                            Direction
                        </label>
                        <select name="sort_direction"
                            class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-blue-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-blue-300"
                            x-model="filters.sort_direction" @change="applyFilters()">
                            <option value="asc" {{ request('sort_direction', 'asc') == 'asc' ? 'selected' : '' }}>
                                ⬆️ Ascending</option>
                            <option value="desc" {{ request('sort_direction') == 'desc' ? 'selected' : '' }}>⬇️
                                Descending</option>
                        </select>
                    </div>

                    <!-- Items Per Page -->
                    <div class="filter-group">
                        <label class="flex items-center text-sm font-semibold text-charcoal-black mb-2">
                            <svg class="w-4 h-4 mr-2 text-leaders-red" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                            </svg>
                            Per Page
                        </label>
                        <select name="per_page"
                            class="w-full px-4 py-3 text-sm font-medium text-charcoal-black bg-white border-2 border-blue-200 rounded-lg shadow-sm focus:border-leaders-red focus:ring-2 focus:ring-leaders-red/10 transition-all duration-200 hover:border-blue-300"
                            x-model="filters.per_page" @change="applyFilters()">
                            <option value="10" {{ request('per_page', 15) == 10 ? 'selected' : '' }}>10 items
                            </option>
                            <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15 items
                            </option>
                            <option value="25" {{ request('per_page', 15) == 25 ? 'selected' : '' }}>25 items
                            </option>
                            <option value="50" {{ request('per_page', 15) == 50 ? 'selected' : '' }}>50 items
                            </option>
                            <option value="100" {{ request('per_page', 15) == 100 ? 'selected' : '' }}>100 items
                            </option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Premium Apply Button -->
            <div class="flex justify-center">
                <button @click="applyFilters()"
                    class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-leaders-red to-leaders-deep-red text-white font-bold text-lg rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 focus:ring-4 focus:ring-leaders-red/25">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Apply All Filters
                    <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5"
                            d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Premium Active Filters Display -->
        <div x-show="hasActiveFilters()" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform translate-y-2"
            x-transition:enter-end="opacity-100 transform translate-y-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-y-0"
            x-transition:leave-end="opacity-0 transform translate-y-2"
            class="mt-8 p-6 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl border border-amber-200">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-lg font-bold text-charcoal-black flex items-center">
                    <svg class="w-5 h-5 mr-2 text-amber-600" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                        </path>
                    </svg>
                    Active Filters
                </h4>
                <button @click="clearAllFilters()"
                    class="inline-flex items-center px-4 py-2 bg-red-100 text-red-700 font-medium text-sm rounded-lg hover:bg-red-200 hover:text-red-800 transition-all duration-200 border border-red-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                        </path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
            <div class="flex flex-wrap gap-3">
                <template x-for="filter in getActiveFilters()" :key="filter.key">
                    <span
                        class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-leaders-red to-leaders-deep-red text-white font-medium text-sm rounded-lg shadow-md hover:shadow-lg transition-all duration-200 group">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span x-text="filter.label"></span>
                        <button @click="removeFilter(filter.key)"
                            class="ml-3 p-1 rounded-full hover:bg-white/20 transition-all duration-200 group-hover:scale-110">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </span>
                </template>
            </div>
        </div>
    </div>
</div>

<script>
    function branchFilters() {
        return {
            showFilters: localStorage.getItem('branchFiltersExpanded') === 'true',
            filters: {
                search: '{{ request('search') }}',
                status: '{{ request('status') }}',
                location: '{{ request('location') }}',
                date_from: '{{ request('date_from') }}',
                date_to: '{{ request('date_to') }}',
                min_students: '{{ request('min_students') }}',
                max_students: '{{ request('max_students') }}',
                min_academies: '{{ request('min_academies') }}',
                max_academies: '{{ request('max_academies') }}',
                sort_by: '{{ request('sort_by', 'name') }}',
                sort_direction: '{{ request('sort_direction', 'asc') }}',
                per_page: '{{ request('per_page', 15) }}'
            },

            init() {
                // Auto-expand filters if any are active
                if (this.hasActiveFilters()) {
                    this.showFilters = true;
                }
            },

            toggleFilters() {
                this.showFilters = !this.showFilters;
                localStorage.setItem('branchFiltersExpanded', this.showFilters);
            },

            applyFilters() {
                const params = new URLSearchParams();

                Object.keys(this.filters).forEach(key => {
                    if (this.filters[key] && this.filters[key] !== '') {
                        params.append(key, this.filters[key]);
                    }
                });

                const url = new URL(window.location);
                url.search = params.toString();
                window.location.href = url.toString();
            },

            clearAllFilters() {
                Object.keys(this.filters).forEach(key => {
                    if (key !== 'sort_by' && key !== 'sort_direction' && key !== 'per_page') {
                        this.filters[key] = '';
                    }
                });
                this.applyFilters();
            },

            hasActiveFilters() {
                return Object.keys(this.filters).some(key => {
                    if (key === 'sort_by' && this.filters[key] === 'name') return false;
                    if (key === 'sort_direction' && this.filters[key] === 'asc') return false;
                    if (key === 'per_page' && this.filters[key] === '15') return false;
                    return this.filters[key] && this.filters[key] !== '';
                });
            },

            getActiveFilters() {
                const activeFilters = [];
                const labels = {
                    search: 'Search',
                    status: 'Status',
                    location: 'Location',
                    date_from: 'From Date',
                    date_to: 'To Date',
                    min_students: 'Min Students',
                    max_students: 'Max Students',
                    min_academies: 'Min Academies',
                    max_academies: 'Max Academies',
                    sort_by: 'Sort By',
                    sort_direction: 'Sort Direction',
                    per_page: 'Per Page'
                };

                Object.keys(this.filters).forEach(key => {
                    if (key === 'sort_by' && this.filters[key] === 'name') return;
                    if (key === 'sort_direction' && this.filters[key] === 'asc') return;
                    if (key === 'per_page' && this.filters[key] === '15') return;

                    if (this.filters[key] && this.filters[key] !== '') {
                        let value = this.filters[key];
                        if (key === 'status') {
                            value = value === '1' ? 'Active' : 'Inactive';
                        }
                        activeFilters.push({
                            key: key,
                            label: `${labels[key]}: ${value}`
                        });
                    }
                });

                return activeFilters;
            },

            removeFilter(key) {
                if (key === 'sort_by') {
                    this.filters[key] = 'name';
                } else if (key === 'sort_direction') {
                    this.filters[key] = 'asc';
                } else if (key === 'per_page') {
                    this.filters[key] = '15';
                } else {
                    this.filters[key] = '';
                }
                this.applyFilters();
            }
        }
    }
</script>
